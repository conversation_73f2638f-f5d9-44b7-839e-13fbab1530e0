package domain

type TblCustomer struct {
	Id        string `gorm:"column:id"`
	Firstname string `gorm:"column:firstname"`
	Lastname  string `gorm:"column:lastname"`
	Email     string `gorm:"column:email"`
	Phone     string `gorm:"column:phone"`
	Gender    string `gorm:"column:gender"`
}

// TableName overrides the table name used by TblCustomer to `Customer`
func (TblCustomer) TableName() string {
	return "Customer"
}
