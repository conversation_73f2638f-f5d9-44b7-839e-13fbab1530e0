package calldb

import (
	"digital-transformation-api/internal/portal/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Port interface {
	Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error)

	// Customer operations
	GetCustomers(request *GetCustomersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomersResponse, errs.Error)
	GetCustomerByID(request *GetCustomerByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomerResponse, errs.Error)
	CreateCustomer(request *CreateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateCustomerResponse, errs.Error)
	UpdateCustomer(request *UpdateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateCustomerResponse, errs.Error)
	DeleteCustomer(request *DeleteCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteCustomerResponse, errs.Error)
}

type Request struct{}

type Response struct{}

// Customer-specific request/response types
type GetCustomersRequest struct {
	Page     int
	PageSize int
	Search   string
	Status   string
}

type GetCustomersResponse struct {
	Customers    []domain.TblCustomer
	TotalRecords int64
}

type GetCustomerByIDRequest struct {
	ID string
}

type GetCustomerResponse struct {
	Customer domain.TblCustomer
}

type CreateCustomerRequest struct {
	Firstname string
	Lastname  string
	Email     string
	Phone     string
	Gender    string
}

type CreateCustomerResponse struct {
	Customer domain.TblCustomer
}

type UpdateCustomerRequest struct {
	ID        string
	Firstname string
	Lastname  string
	Email     string
	Phone     string
	Gender    string
}

type UpdateCustomerResponse struct {
	Customer domain.TblCustomer
}

type DeleteCustomerRequest struct {
	ID string
}

type DeleteCustomerResponse struct {
	Success bool
}
