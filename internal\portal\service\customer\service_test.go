package customer

import (
	"digital-transformation-api/internal/portal/domain"
	"testing"
)

func TestMapTblCustomerToCustomerInfo(t *testing.T) {
	// Test data
	tblCustomer := domain.TblCustomer{
		Id:        "cust-123",
		Firstname: "<PERSON>",
		Lastname:  "<PERSON><PERSON>",
		Email:     "<EMAIL>",
		Phone:     "+1234567890",
		Gender:    "active",
	}

	// Test the mapping function
	customerInfo := mapTblCustomerToCustomerInfo(tblCustomer)

	// Verify the mapping
	if customerInfo.ID != tblCustomer.Id {
		t.Errorf("Expected ID %s, got %s", tblCustomer.Id, customerInfo.ID)
	}

	expectedName := "John Doe"
	if customerInfo.Name != expectedName {
		t.Errorf("Expected Name %s, got %s", expectedName, customerInfo.Name)
	}

	if customerInfo.Email != tblCustomer.Email {
		t.<PERSON>rf("Expected Email %s, got %s", tblCustomer.Email, customerInfo.Email)
	}

	if customerInfo.Phone != tblCustomer.Phone {
		t.Errorf("Expected Phone %s, got %s", tblCustomer.Phone, customerInfo.Phone)
	}

	if customerInfo.Status != tblCustomer.Gender {
		t.Errorf("Expected Status %s, got %s", tblCustomer.Gender, customerInfo.Status)
	}
}

func TestMapTblCustomerToCustomerInfo_EmptyLastname(t *testing.T) {
	// Test data with empty lastname
	tblCustomer := domain.TblCustomer{
		Id:        "cust-456",
		Firstname: "Jane",
		Lastname:  "",
		Email:     "<EMAIL>",
		Phone:     "+0987654321",
		Gender:    "inactive",
	}

	// Test the mapping function
	customerInfo := mapTblCustomerToCustomerInfo(tblCustomer)

	// Verify the mapping - should only have firstname
	expectedName := "Jane"
	if customerInfo.Name != expectedName {
		t.Errorf("Expected Name %s, got %s", expectedName, customerInfo.Name)
	}
}

func TestMapTblCustomerToCustomerInfo_EmptyFirstname(t *testing.T) {
	// Test data with empty firstname
	tblCustomer := domain.TblCustomer{
		Id:        "cust-789",
		Firstname: "",
		Lastname:  "Smith",
		Email:     "<EMAIL>",
		Phone:     "+1122334455",
		Gender:    "active",
	}

	// Test the mapping function
	customerInfo := mapTblCustomerToCustomerInfo(tblCustomer)

	// Verify the mapping - should only have lastname
	expectedName := "Smith"
	if customerInfo.Name != expectedName {
		t.Errorf("Expected Name %s, got %s", expectedName, customerInfo.Name)
	}
}
