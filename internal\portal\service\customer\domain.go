package customer

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Service interface {
	
	// GET /customer - Get customer list with query parameters
	GetCustomers(request *GetCustomersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomersResponse, errs.Error)

	// GET /customer/:id - Get customer by ID
	GetCustomerByID(request *GetCustomerByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomerResponse, errs.Error)

	// POST /customer - Create new customer
	CreateCustomer(request *CreateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateCustomerResponse, errs.Error)

	// PUT /customer/:id - Update customer
	UpdateCustomer(request *UpdateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateCustomerResponse, errs.<PERSON><PERSON>r)

	// DELETE /customer/:id - Delete customer
	DeleteCustomer(request *DeleteCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteCustomerResponse, errs.Error)
}

// GET /customer - Query parameters for listing members
type GetCustomersRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1" json:"page"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100" json:"page_size"`
	Search   string `form:"search" binding:"omitempty,max=100" json:"search"`
	Status   string `form:"status" binding:"omitempty,oneof=active inactive" json:"status"`
}

type GetCustomersResponse struct {
	Customers    []CustomerInfo `json:"members"`
	Pagination Pagination   `json:"pagination"`
}

// GET /customer/:id - Path parameter for getting customer by ID
type GetCustomerByIDRequest struct {
	ID string `uri:"id" binding:"required,uuid" json:"id"`
}

type GetCustomerResponse struct {
	Customer CustomerInfo `json:"customer"`
}

// POST /customer - JSON body for creating customer
type CreateCustomerRequest struct {
	Name     string                 `json:"name" binding:"required,min=2,max=100" validate:"required"`
	Email    string                 `json:"email" binding:"required,email" validate:"required,email"`
	Phone    string                 `json:"phone" binding:"omitempty,min=10,max=15" validate:"omitempty"`
	Status   string                 `json:"status" binding:"omitempty,oneof=active inactive" validate:"omitempty,oneof=active inactive"`
	Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

type CreateCustomerResponse struct {
	Customer  CustomerInfo `json:"customer"`
	Message string     `json:"message"`
}

// PUT /customer/:id - Path parameter + JSON body for updating customer
type UpdateCustomerRequest struct {
	ID       string                 `uri:"id" binding:"required,uuid" json:"id"`
	Name     string                 `json:"name" binding:"omitempty,min=2,max=100" validate:"omitempty"`
	Email    string                 `json:"email" binding:"omitempty,email" validate:"omitempty,email"`
	Phone    string                 `json:"phone" binding:"omitempty,min=10,max=15" validate:"omitempty"`
	Status   string                 `json:"status" binding:"omitempty,oneof=active inactive" validate:"omitempty,oneof=active inactive"`
	Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

type UpdateCustomerResponse struct {
	Customer  CustomerInfo `json:"customer"`
	Message string     `json:"message"`
}

// DELETE /customer/:id - Path parameter for deleting customer
type DeleteCustomerRequest struct {
	ID string `uri:"id" binding:"required,uuid" json:"id"`
}

type DeleteCustomerResponse struct {
	Message string `json:"message"`
}

// Common structures
type CustomerInfo struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Email     string                 `json:"email"`
	Phone     string                 `json:"phone,omitempty"`
	Status    string                 `json:"status"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt string                 `json:"created_at"`
	UpdatedAt string                 `json:"updated_at"`
}

type Pagination struct {
	Page      int   `json:"page"`
	PageSize  int   `json:"page_size"`
	Total     int64 `json:"total"`
	TotalPage int   `json:"total_page"`
}

// Legacy support - keeping original structures for backward compatibility
type Request struct{}
type Response struct{}
