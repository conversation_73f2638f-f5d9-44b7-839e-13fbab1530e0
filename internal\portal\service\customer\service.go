package customer

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/portal/port/call-api"
	calldb "digital-transformation-api/internal/portal/port/call-db"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	callDb  calldb.Port
	callApi callapi.Port
}

func New(
	callDb calldb.Port,
	callApi callapi.Port,
) Service {
	return &service{
		callDb:  callDb,
		callApi: callApi,
	}
}

// GET /customer - Get customer list with query parameters
func (s *service) GetCustomers(request *GetCustomersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomersResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.<PERSON>rrorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Set default values
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 {
		request.PageSize = 10
	}

	// Call database layer to get members
	dbRequest := &calldb.Request{
		// Map request to database request structure
		// This would contain the actual query parameters
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to get members from database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	members := []CustomerInfo{
		{
			ID:        "123e4567-e89b-12d3-a456-426614174000",
			Name:      "John Doe",
			Email:     "<EMAIL>",
			Phone:     "+1234567890",
			Status:    "active",
			CreatedAt: "2024-01-01T00:00:00Z",
			UpdatedAt: "2024-01-01T00:00:00Z",
		},
	}

	totalRecords := int64(1)
	totalPages := int((totalRecords + int64(request.PageSize) - 1) / int64(request.PageSize))

	response := &GetCustomersResponse{
		Customers: members,
		Pagination: Pagination{
			Page:      request.Page,
			PageSize:  request.PageSize,
			Total:     totalRecords,
			TotalPage: totalPages,
		},
	}

	l.Infof("successfully retrieved %d members", len(members))
	return response, nil
}

// GET /customer/:id - Get customer by ID
func (s *service) GetCustomerByID(request *GetCustomerByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomerResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to get customer by ID
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to get customer by ID from database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	customer := CustomerInfo{
		ID:        request.ID,
		Name:      "John Doe",
		Email:     "<EMAIL>",
		Phone:     "+1234567890",
		Status:    "active",
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T00:00:00Z",
	}

	response := &GetCustomerResponse{
		Customer: customer,
	}

	l.Infof("successfully retrieved customer with ID: %s", request.ID)
	return response, nil
}

// POST /customer - Create new customer
func (s *service) CreateCustomer(request *CreateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateCustomerResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Set default status if not provided
	if request.Status == "" {
		request.Status = "active"
	}

	// Call database layer to create customer
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to create customer in database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	customer := CustomerInfo{
		ID:        "123e4567-e89b-12d3-a456-************", // Generated UUID
		Name:      request.Name,
		Email:     request.Email,
		Phone:     request.Phone,
		Status:    request.Status,
		Metadata:  request.Metadata,
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T00:00:00Z",
	}

	response := &CreateCustomerResponse{
		Customer:  customer,
		Message: "Customer created successfully",
	}

	l.Infof("successfully created customer with email: %s", request.Email)
	return response, nil
}

// PUT /customer/:id - Update customer
func (s *service) UpdateCustomer(request *UpdateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateCustomerResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to update customer
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to update customer in database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	customer := CustomerInfo{
		ID:        request.ID,
		Name:      request.Name,
		Email:     request.Email,
		Phone:     request.Phone,
		Status:    request.Status,
		Metadata:  request.Metadata,
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T12:00:00Z", // Updated timestamp
	}

	response := &UpdateCustomerResponse{
		Customer:  customer,
		Message: "Customer updated successfully",
	}

	l.Infof("successfully updated customer with ID: %s", request.ID)
	return response, nil
}

// DELETE /customer/:id - Delete customer
func (s *service) DeleteCustomer(request *DeleteCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteCustomerResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to delete customer
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to delete customer from database: %v", err)
		return nil, err
	}

	response := &DeleteCustomerResponse{
		Message: "Customer deleted successfully",
	}

	l.Infof("successfully deleted customer with ID: %s", request.ID)
	return response, nil
}
