# Customer Table Mapping Integration

## Overview

This document describes the integration of the `TblCustomer` domain model with the customer service layer, enabling real database operations instead of mock data.

## Architecture

### Domain Layer
- **File**: `internal/portal/domain/tbl-customer.go`
- **Purpose**: Defines the database table structure using GORM tags
- **Table Name**: `Customer` (overridden via `TableName()` method)

```go
type TblCustomer struct {
    Id        string `gorm:"column:id"`
    Firstname string `gorm:"column:firstname"`
    Lastname  string `gorm:"column:lastname"`
    Email     string `gorm:"column:email"`
    Phone     string `gorm:"column:phone"`
    Gender    string `gorm:"column:gender"`
}
```

### Database Port Layer
- **Files**: 
  - `internal/portal/port/call-db/port.go` - Interface definitions
  - `internal/portal/port/call-db/adaptor-pg.go` - PostgreSQL implementation

### Service Layer
- **File**: `internal/portal/service/customer/service.go`
- **Purpose**: Business logic and mapping between domain and API models

## Key Features

### 1. CRUD Operations
All customer operations now use actual database queries:
- **GET /customer** - List customers with pagination and search
- **GET /customer/:id** - Get customer by ID
- **POST /customer** - Create new customer
- **PUT /customer/:id** - Update existing customer
- **DELETE /customer/:id** - Delete customer

### 2. Data Mapping
Automatic mapping between database model (`TblCustomer`) and API model (`CustomerInfo`):
- Combines `firstname` + `lastname` → `name`
- Maps `gender` field → `status` field
- Handles empty name parts gracefully

### 3. Search and Filtering
- Search across firstname, lastname, and email fields
- Filter by status (using gender field)
- Pagination support

### 4. Error Handling
- Proper error responses for not found records
- Database error handling
- Validation error handling

## Database Schema

The integration expects a PostgreSQL table with this structure:

```sql
CREATE TABLE Customer (
    id VARCHAR PRIMARY KEY,
    firstname VARCHAR,
    lastname VARCHAR,
    email VARCHAR,
    phone VARCHAR,
    gender VARCHAR
);
```

## API Examples

### Create Customer
```bash
POST /customer
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "status": "active"
}
```

### Get Customers with Search
```bash
GET /customer?page=1&page_size=10&search=john&status=active
```

### Update Customer
```bash
PUT /customer/cust-123
Content-Type: application/json

{
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "status": "active"
}
```

## Testing

Run the tests to verify the mapping functionality:

```bash
go test ./internal/portal/service/customer -v
```

## Configuration

Make sure your database configuration in `configs/config.yaml` is properly set:

```yaml
db:
  main:
    driver: pg
    dsn: postgres://username:password@localhost:5432/database?sslmode=disable
    maxIdleConns: 5
    maxConns: 10
    maxLifeTime: 1h
```

## Next Steps

1. **Add Timestamps**: Consider adding `created_at` and `updated_at` fields to `TblCustomer`
2. **Add Indexes**: Create database indexes for better search performance
3. **Add Validation**: Implement more comprehensive validation rules
4. **Add Soft Delete**: Implement soft delete functionality if needed
5. **Add Audit Trail**: Track changes to customer records
