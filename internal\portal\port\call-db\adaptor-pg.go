package calldb

import (
	"digital-transformation-api/internal/portal/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{
		db: db,
	}
}

func (a *adaptorPG) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	_ = tx

	return nil, nil
}

// GetCustomers retrieves customers from database with pagination and filtering
func (a *adaptorPG) GetCustomers(request *GetCustomersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomersResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var customers []domain.TblCustomer
	var totalRecords int64

	// Build query
	query := tx.Model(&domain.TblCustomer{})

	// Apply search filter if provided
	if request.Search != "" {
		searchTerm := "%" + strings.ToLower(request.Search) + "%"
		query = query.Where("LOWER(firstname) LIKE ? OR LOWER(lastname) LIKE ? OR LOWER(email) LIKE ?",
			searchTerm, searchTerm, searchTerm)
	}

	// Apply status filter if provided (assuming gender field is used for status)
	if request.Status != "" {
		query = query.Where("gender = ?", request.Status)
	}

	// Get total count
	if err := query.Count(&totalRecords).Error; err != nil {
		l.Errorf("failed to count customers: %v", err)
		return nil, errs.NewInternalError()
	}

	// Apply pagination
	offset := (request.Page - 1) * request.PageSize
	if err := query.Offset(offset).Limit(request.PageSize).Find(&customers).Error; err != nil {
		l.Errorf("failed to get customers: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetCustomersResponse{
		Customers:    customers,
		TotalRecords: totalRecords,
	}, nil
}

// GetCustomerByID retrieves a single customer by ID
func (a *adaptorPG) GetCustomerByID(request *GetCustomerByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetCustomerResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var customer domain.TblCustomer
	if err := tx.Where("id = ?", request.ID).First(&customer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("customer not found with ID: %s", request.ID)
			return nil, errs.NewCustom(404, "40404", "Customer not found", "")
		}
		l.Errorf("failed to get customer by ID: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetCustomerResponse{
		Customer: customer,
	}, nil
}

// CreateCustomer creates a new customer in the database
func (a *adaptorPG) CreateCustomer(request *CreateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateCustomerResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	customer := domain.TblCustomer{
		Id:        fmt.Sprintf("cust-%d", time.Now().UnixNano()), // Simple ID generation
		Firstname: request.Firstname,
		Lastname:  request.Lastname,
		Email:     request.Email,
		Phone:     request.Phone,
		Gender:    request.Gender,
	}

	if err := tx.Create(&customer).Error; err != nil {
		l.Errorf("failed to create customer: %v", err)
		return nil, errs.NewInternalError()
	}

	return &CreateCustomerResponse{
		Customer: customer,
	}, nil
}

// UpdateCustomer updates an existing customer in the database
func (a *adaptorPG) UpdateCustomer(request *UpdateCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateCustomerResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	// First check if customer exists
	var existingCustomer domain.TblCustomer
	if err := tx.Where("id = ?", request.ID).First(&existingCustomer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("customer not found with ID: %s", request.ID)
			return nil, errs.NewCustom(404, "40404", "Customer not found", "")
		}
		l.Errorf("failed to find customer for update: %v", err)
		return nil, errs.NewInternalError()
	}

	// Update fields
	updates := map[string]interface{}{}
	if request.Firstname != "" {
		updates["firstname"] = request.Firstname
	}
	if request.Lastname != "" {
		updates["lastname"] = request.Lastname
	}
	if request.Email != "" {
		updates["email"] = request.Email
	}
	if request.Phone != "" {
		updates["phone"] = request.Phone
	}
	if request.Gender != "" {
		updates["gender"] = request.Gender
	}

	if err := tx.Model(&existingCustomer).Updates(updates).Error; err != nil {
		l.Errorf("failed to update customer: %v", err)
		return nil, errs.NewInternalError()
	}

	// Fetch updated customer
	var updatedCustomer domain.TblCustomer
	if err := tx.Where("id = ?", request.ID).First(&updatedCustomer).Error; err != nil {
		l.Errorf("failed to fetch updated customer: %v", err)
		return nil, errs.NewInternalError()
	}

	return &UpdateCustomerResponse{
		Customer: updatedCustomer,
	}, nil
}

// DeleteCustomer deletes a customer from the database
func (a *adaptorPG) DeleteCustomer(request *DeleteCustomerRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteCustomerResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	// First check if customer exists
	var customer domain.TblCustomer
	if err := tx.Where("id = ?", request.ID).First(&customer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("customer not found with ID: %s", request.ID)
			return nil, errs.NewCustom(404, "40404", "Customer not found", "")
		}
		l.Errorf("failed to find customer for deletion: %v", err)
		return nil, errs.NewInternalError()
	}

	// Delete the customer
	if err := tx.Delete(&customer).Error; err != nil {
		l.Errorf("failed to delete customer: %v", err)
		return nil, errs.NewInternalError()
	}

	return &DeleteCustomerResponse{
		Success: true,
	}, nil
}
