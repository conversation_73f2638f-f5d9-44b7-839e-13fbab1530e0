package handler

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/portal/port/call-api"
	calldb "digital-transformation-api/internal/portal/port/call-db"
	"digital-transformation-api/internal/portal/service/customer"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type memberHandler struct {
	service customer.Service
}

func NewCustomerHandler(service customer.Service) *memberHandler {
	return &memberHandler{
		service: service,
	}
}

// GET /customer - Handle getting customer list with query parameters
func (h *memberHandler) GetCustomers(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request customer.GetCustomersRequest

	// Bind query parameters using ShouldBindQuery
	if err := ctx.ShouldBindQuery(&request); err != nil {
		l.<PERSON>("failed when bind query parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received get members request: page=%d, pageSize=%d, search=%s, status=%s",
		request.Page, request.PageSize, request.Search, request.Status)

	response, err := h.service.GetCustomers(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// GET /customer/:id - Handle getting customer by ID with path parameter
func (h *memberHandler) GetCustomerByID(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request customer.GetCustomerByIDRequest

	// Bind URI parameters using ShouldBindUri
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received get customer by ID request: id=%s", request.ID)

	response, err := h.service.GetCustomerByID(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// POST /customer - Handle creating new customer with JSON body
func (h *memberHandler) CreateCustomer(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request customer.CreateCustomerRequest

	// Bind JSON body using ShouldBindJSON
	if err := ctx.ShouldBindJSON(&request); err != nil {
		l.Errorf("failed when bind JSON request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received create customer request: name=%s, email=%s", request.Name, request.Email)

	response, err := h.service.CreateCustomer(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	// Return 201 Created for successful resource creation
	ctx.JSON(http.StatusCreated, response)
}

// PUT /customer/:id - Handle updating customer with path parameter and JSON body
func (h *memberHandler) UpdateCustomer(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request customer.UpdateCustomerRequest

	// First bind URI parameters
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	// Then bind JSON body - this will merge with the URI parameters
	if err := ctx.ShouldBindJSON(&request); err != nil {
		l.Errorf("failed when bind JSON request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received update customer request: id=%s, name=%s, email=%s",
		request.ID, request.Name, request.Email)

	response, err := h.service.UpdateCustomer(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// DELETE /customer/:id - Handle deleting customer with path parameter
func (h *memberHandler) DeleteCustomer(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request customer.DeleteCustomerRequest

	// Bind URI parameters using ShouldBindUri
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received delete customer request: id=%s", request.ID)

	response, err := h.service.DeleteCustomer(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	// Return 200 OK with success message for DELETE operations
	ctx.JSON(http.StatusOK, response)
}

func BindCustomerRoute(app gins.GinApps) {
	svc := customer.New(
		calldb.NewAdaptorPG(infrastructure.Db),
		callapi.NewAdaptorAPI(callapi.NewClient()),
	)

	hdl := NewCustomerHandler(svc)

	// Modern RESTful routes demonstrating complete HTTP request-response flow

	// GET /customer - Get customer list with query parameters
	// Example: GET /customer?page=1&page_size=10&search=john&status=active
	app.Register(
		http.MethodGet,
		"/customer",
		app.ParseRouteContext(hdl.GetCustomers),
	)

	// GET /customer/:id - Get customer by ID with path parameter
	// Example: GET /customer/123e4567-e89b-12d3-a456-426614174000
	app.Register(
		http.MethodGet,
		"/customer/:id",
		app.ParseRouteContext(hdl.GetCustomerByID),
	)

	// POST /customer - Create new customer with JSON body
	// Example: POST /customer with JSON body
	app.Register(
		http.MethodPost,
		"/customer",
		app.ParseRouteContext(hdl.CreateCustomer),
	)

	// PUT /customer/:id - Update customer with path parameter and JSON body
	// Example: PUT /customer/123e4567-e89b-12d3-a456-426614174000 with JSON body
	app.Register(
		http.MethodPut,
		"/customer/:id",
		app.ParseRouteContext(hdl.UpdateCustomer),
	)

	// DELETE /customer/:id - Delete customer with path parameter
	// Example: DELETE /customer/123e4567-e89b-12d3-a456-426614174000
	app.Register(
		http.MethodDelete,
		"/customer/:id",
		app.ParseRouteContext(hdl.DeleteCustomer),
	)
}
